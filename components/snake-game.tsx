"use client";
import { useEffect, useRef, useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Play, Pause, RotateCcw } from 'lucide-react';
interface Position {
  x: number;
  y: number;
}
interface GameState {
  snake: Position[];
  food: Position;
  direction: Position;
  gameOver: boolean;
  score: number;
  isPlaying: boolean;
}
const GRID_SIZE = 20;
const CANVAS_WIDTH = 400;
const CANVAS_HEIGHT = 300;
const FOOD_SIZE = 24; // Slightly bigger than grid but safe
export function SnakeGame() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const profileImageRef = useRef<HTMLImageElement | null>(null);
  const gameLoopRef = useRef<number>();
  const [gameState, setGameState] = useState<GameState>({
    snake: [{ x: 10, y: 10 }],
    food: { x: Math.floor(CANVAS_WIDTH / GRID_SIZE / 2), y: Math.floor(CANVAS_HEIGHT / GRID_SIZE / 2) }, // Center of canvas
    direction: { x: 0, y: 0 },
    gameOver: false,
    score: 0,
    isPlaying: false
  });
  // Load profile image
  useEffect(() => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    img.onload = () => {
      profileImageRef.current = img;
    };
    img.src = 'https://avatars.githubusercontent.com/u/92255945?v=4';
  }, []);
  // Generate random food position
  const generateFood = useCallback((): Position => {
    // Simple approach: keep food well within boundaries
    const gridWidth = Math.floor(CANVAS_WIDTH / GRID_SIZE); // 20
    const gridHeight = Math.floor(CANVAS_HEIGHT / GRID_SIZE); // 15
    // Use 2 grid margin from all sides to be absolutely safe
    const margin = 2;
    const minX = margin;
    const maxX = gridWidth - margin - 1; // -1 for 0-based indexing
    const minY = margin;
    const maxY = gridHeight - margin - 1;
    // Debug: log the ranges
    console.log('Food generation ranges:', { minX, maxX, minY, maxY });
    const x = Math.floor(Math.random() * (maxX - minX + 1)) + minX;
    const y = Math.floor(Math.random() * (maxY - minY + 1)) + minY;
    console.log('Generated food position:', { x, y });
    return { x, y };
  }, []);
  // Draw game
  const draw = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    // Clear canvas
    ctx.fillStyle = '#000';
    ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
    // Draw snake
    ctx.fillStyle = '#4ade80';
    gameState.snake.forEach((segment, index) => {
      if (index === 0) {
        // Snake head - slightly different color
        ctx.fillStyle = '#22c55e';
      } else {
        ctx.fillStyle = '#4ade80';
      }
      ctx.fillRect(
        segment.x * GRID_SIZE,
        segment.y * GRID_SIZE,
        GRID_SIZE - 2,
        GRID_SIZE - 2
      );
    });
    // Draw food (profile image)
    if (profileImageRef.current) {
      const foodX = gameState.food.x * GRID_SIZE - (FOOD_SIZE - GRID_SIZE) / 2;
      const foodY = gameState.food.y * GRID_SIZE - (FOOD_SIZE - GRID_SIZE) / 2;
      ctx.save();
      ctx.beginPath();
      ctx.arc(
        foodX + FOOD_SIZE / 2,
        foodY + FOOD_SIZE / 2,
        FOOD_SIZE / 2,
        0,
        Math.PI * 2
      );
      ctx.clip();
      ctx.drawImage(
        profileImageRef.current,
        foodX,
        foodY,
        FOOD_SIZE,
        FOOD_SIZE
      );
      ctx.restore();
    } else {
      // Fallback food
      ctx.fillStyle = '#ef4444';
      ctx.fillRect(
        gameState.food.x * GRID_SIZE,
        gameState.food.y * GRID_SIZE,
        GRID_SIZE,
        GRID_SIZE
      );
    }
    // Draw border
    ctx.strokeStyle = '#374151';
    ctx.lineWidth = 2;
    ctx.strokeRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
  }, [gameState]);
  // Game logic
  const updateGame = useCallback(() => {
    if (!gameState.isPlaying || gameState.gameOver) return;
    setGameState(prevState => {
      // Don't move if no direction is set
      if (prevState.direction.x === 0 && prevState.direction.y === 0) {
        return prevState;
      }
      const newSnake = [...prevState.snake];
      const head = { ...newSnake[0] };
      // Move head
      head.x += prevState.direction.x;
      head.y += prevState.direction.y;
      // Check wall collision
      if (
        head.x < 0 ||
        head.x >= CANVAS_WIDTH / GRID_SIZE ||
        head.y < 0 ||
        head.y >= CANVAS_HEIGHT / GRID_SIZE
      ) {
        return { ...prevState, gameOver: true, isPlaying: false };
      }
      // Check self collision
      if (newSnake.some(segment => segment.x === head.x && segment.y === head.y)) {
        return { ...prevState, gameOver: true, isPlaying: false };
      }
      newSnake.unshift(head);
      // Check food collision
      if (head.x === prevState.food.x && head.y === prevState.food.y) {
        return {
          ...prevState,
          snake: newSnake,
          food: generateFood(),
          score: prevState.score + 10
        };
      } else {
        newSnake.pop();
        return {
          ...prevState,
          snake: newSnake
        };
      }
    });
  }, [gameState.isPlaying, gameState.gameOver, generateFood]);
  // Game loop
  useEffect(() => {
    if (gameState.isPlaying && !gameState.gameOver) {
      gameLoopRef.current = window.setInterval(updateGame, 150);
    } else {
      if (gameLoopRef.current) {
        clearInterval(gameLoopRef.current);
      }
    }
    return () => {
      if (gameLoopRef.current) {
        clearInterval(gameLoopRef.current);
      }
    };
  }, [gameState.isPlaying, gameState.gameOver, updateGame]);
  // Draw game
  useEffect(() => {
    draw();
  }, [draw]);
  // Handle keyboard input
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!gameState.isPlaying) return;
      const { direction } = gameState;
      switch (e.key) {
        case 'ArrowUp':
          if (direction.y === 0) {
            setGameState(prev => ({ ...prev, direction: { x: 0, y: -1 } }));
          }
          break;
        case 'ArrowDown':
          if (direction.y === 0) {
            setGameState(prev => ({ ...prev, direction: { x: 0, y: 1 } }));
          }
          break;
        case 'ArrowLeft':
          if (direction.x === 0) {
            setGameState(prev => ({ ...prev, direction: { x: -1, y: 0 } }));
          }
          break;
        case 'ArrowRight':
          if (direction.x === 0) {
            setGameState(prev => ({ ...prev, direction: { x: 1, y: 0 } }));
          }
          break;
      }
    };
    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [gameState.isPlaying, gameState.direction]);
  const startGame = () => {
    setGameState(prev => ({
      ...prev,
      isPlaying: true,
      // Set initial direction to right if no direction is set
      direction: prev.direction.x === 0 && prev.direction.y === 0 ? { x: 1, y: 0 } : prev.direction
    }));
  };
  const pauseGame = () => {
    setGameState(prev => ({ ...prev, isPlaying: false }));
  };
  const resetGame = () => {
    setGameState({
      snake: [{ x: 10, y: 10 }],
      food: { x: Math.floor(CANVAS_WIDTH / GRID_SIZE / 2), y: Math.floor(CANVAS_HEIGHT / GRID_SIZE / 2) }, // Center of canvas
      direction: { x: 0, y: 0 },
      gameOver: false,
      score: 0,
      isPlaying: false
    });
  };
  return (
    <div className="flex flex-col items-center space-y-4 p-4 bg-accent/20 rounded-lg">
      <div className="text-center">
        <p className="text-sm text-muted-foreground mb-2">
          Eat my profile pictures to grow! Use arrow keys to move.
        </p>
        <p className="text-sm font-medium">Score: {gameState.score}</p>
      </div>
      <canvas
        ref={canvasRef}
        width={CANVAS_WIDTH}
        height={CANVAS_HEIGHT}
        className="border-2 border-border rounded-lg"
      />
      <div className="flex gap-2">
        {!gameState.isPlaying && !gameState.gameOver && (
          <Button onClick={startGame} size="sm">
            <Play className="w-4 h-4 mr-1" />
            Start
          </Button>
        )}
        {gameState.isPlaying && (
          <Button onClick={pauseGame} size="sm" variant="outline">
            <Pause className="w-4 h-4 mr-1" />
            Pause
          </Button>
        )}
        <Button onClick={resetGame} size="sm" variant="outline">
          <RotateCcw className="w-4 h-4 mr-1" />
          Reset
        </Button>
      </div>
      {gameState.gameOver && (
        <div className="text-center">
          <p className="text-red-500 font-semibold">Game Over!</p>
          <p className="text-sm text-muted-foreground">Final Score: {gameState.score}</p>
        </div>
      )}
    </div>
  );
}
