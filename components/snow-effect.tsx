"use client";

import { useEffect, useRef } from 'react';

interface Snowflake {
  x: number;
  y: number;
  size: number;
  speed: number;
  opacity: number;
  drift: number;
}

export function useSnowEffect() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const snowflakes = useRef<Snowflake[]>([]);
  const mousePos = useRef({ x: 0, y: 0 });
  const animationId = useRef<number>();
  const profileImage = useRef<HTMLImageElement | null>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Canvas boyutunu ayarla
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Profil resmini yükle
    const loadProfileImage = () => {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      img.onload = () => {
        profileImage.current = img;
      };
      img.src = 'https://avatars.githubusercontent.com/u/92255945?v=4';
    };

    loadProfileImage();

    // Kar taneleri oluştur
    const createSnowflakes = () => {
      const count = Math.floor((canvas.width * canvas.height) / 15000); // Daha az kar tanesi
      snowflakes.current = [];

      for (let i = 0; i < count; i++) {
        snowflakes.current.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          size: Math.random() * 8 + 6, // Daha büyük kar taneleri (6-14px)
          speed: Math.random() * 2 + 0.5,
          opacity: Math.random() * 0.6 + 0.4, // Daha opak
          drift: Math.random() * 0.5 - 0.25
        });
      }
    };

    createSnowflakes();

    // Mouse hareket takibi
    const handleMouseMove = (e: MouseEvent) => {
      mousePos.current = { x: e.clientX, y: e.clientY };
    };

    window.addEventListener('mousemove', handleMouseMove);

    // Animasyon döngüsü
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      snowflakes.current.forEach((flake) => {
        // Mouse etkisi
        const dx = mousePos.current.x - flake.x;
        const dy = mousePos.current.y - flake.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < 100) {
          const force = (100 - distance) / 100;
          flake.x -= (dx / distance) * force * 2;
          flake.y -= (dy / distance) * force * 2;
        }

        // Normal hareket
        flake.y += flake.speed;
        flake.x += flake.drift;

        // Ekran sınırları kontrolü
        if (flake.y > canvas.height) {
          flake.y = -10;
          flake.x = Math.random() * canvas.width;
        }
        if (flake.x > canvas.width) {
          flake.x = 0;
        } else if (flake.x < 0) {
          flake.x = canvas.width;
        }

        // Kar tanesini çiz (profil resmi olarak)
        ctx.save();
        ctx.globalAlpha = flake.opacity;

        if (profileImage.current) {
          // Circular clipping için
          ctx.beginPath();
          ctx.arc(flake.x, flake.y, flake.size, 0, Math.PI * 2);
          ctx.clip();

          // Profil resmini çiz
          const imageSize = flake.size * 2;
          ctx.drawImage(
            profileImage.current,
            flake.x - flake.size,
            flake.y - flake.size,
            imageSize,
            imageSize
          );
        } else {
          // Fallback: beyaz daire
          ctx.fillStyle = 'white';
          ctx.beginPath();
          ctx.arc(flake.x, flake.y, flake.size, 0, Math.PI * 2);
          ctx.fill();
        }

        ctx.restore();
      });

      animationId.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      window.removeEventListener('mousemove', handleMouseMove);
      if (animationId.current) {
        cancelAnimationFrame(animationId.current);
      }
    };
  }, []);

  return canvasRef;
}

export function SnowEffect() {
  const canvasRef = useSnowEffect();

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 pointer-events-none z-0"
      style={{ background: 'transparent' }}
    />
  );
}
